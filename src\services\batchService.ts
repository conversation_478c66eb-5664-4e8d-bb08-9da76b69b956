import { BatchItem, BatchProcessingState, EnhancementStyle } from "../types";
import { geminiService } from "./geminiService";

export class BatchService {
  private processingAbortController: AbortController | null = null;

  async parseTextFile(file: File): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        try {
          const text = event.target?.result as string;
          const lines = text
            .split(/\r?\n/)
            .map((line) => line.trim())
            .filter((line) => line.length > 0);

          resolve(lines);
        } catch (error) {
          reject(new Error("Failed to parse file"));
        }
      };

      reader.onerror = () => {
        reject(new Error("Failed to read file"));
      };

      reader.readAsText(file);
    });
  }

  createBatchItems(prompts: string[]): BatchItem[] {
    return prompts.map((prompt, index) => ({
      id: `batch-${Date.now()}-${index}`,
      originalPrompt: prompt,
      status: "pending",
    }));
  }

  async processBatch(
    items: BatchItem[],
    style: EnhancementStyle,
    onProgress: (state: BatchProcessingState) => void,
    onComplete: (results: BatchItem[]) => void
  ): Promise<void> {
    this.processingAbortController = new AbortController();

    const state: BatchProcessingState = {
      items: [...items],
      isProcessing: true,
      currentIndex: 0,
      totalItems: items.length,
      completedItems: 0,
      failedItems: 0,
    };

    onProgress(state);

    try {
      for (let i = 0; i < items.length; i++) {
        if (this.processingAbortController.signal.aborted) {
          break;
        }

        const item = state.items[i];
        state.currentIndex = i;

        // Update item status to processing
        item.status = "processing";
        onProgress({ ...state });

        try {
          const response = await geminiService.enhancePrompt({
            originalPrompt: item.originalPrompt,
            mode: "quick", // Batch processing uses quick mode
            style,
          });

          item.enhancedPrompt = response.enhancedPrompt;
          item.status = "completed";
          state.completedItems++;
        } catch (error) {
          item.status = "error";
          item.error = error instanceof Error ? error.message : "Unknown error";
          state.failedItems++;
        }

        onProgress({ ...state });

        // Add a small delay to prevent rate limiting
        if (i < items.length - 1) {
          await this.delay(500);
        }
      }
    } catch (error) {
      console.error("Batch processing error:", error);
    } finally {
      state.isProcessing = false;
      state.currentIndex = state.totalItems;
      onProgress({ ...state });
      onComplete(state.items);
    }
  }

  stopProcessing(): void {
    if (this.processingAbortController) {
      this.processingAbortController.abort();
    }
  }

  async exportResults(
    items: BatchItem[],
    format: "json" | "csv" | "txt" = "json"
  ): Promise<string> {
    const completedItems = items.filter((item) => item.status === "completed");

    switch (format) {
      case "json":
        return JSON.stringify(completedItems, null, 2);

      case "csv":
        const headers = [
          "Original Prompt",
          "Enhanced Prompt",
          "Status",
          "Error",
        ];
        const rows = items.map((item) => [
          `"${item.originalPrompt.replace(/"/g, '""')}"`,
          `"${(item.enhancedPrompt || "").replace(/"/g, '""')}"`,
          item.status,
          `"${(item.error || "").replace(/"/g, '""')}"`,
        ]);

        return [headers.join(","), ...rows.map((row) => row.join(","))].join(
          "\n"
        );

      case "txt":
        return items
          .map((item, index) => {
            let result = `=== Item ${index + 1} ===\n`;
            result += `Status: ${item.status}\n`;
            result += `Original: ${item.originalPrompt}\n`;

            if (item.enhancedPrompt) {
              result += `Enhanced: ${item.enhancedPrompt}\n`;
            }

            if (item.error) {
              result += `Error: ${item.error}\n`;
            }

            return result;
          })
          .join("\n\n");

      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  async downloadFile(
    content: string,
    filename: string,
    mimeType: string
  ): Promise<void> {
    if (window.electronAPI) {
      // In Electron, use the main process to save files
      try {
        const result = await window.electronAPI.showSaveDialog();
        if (!result.canceled && result.filePath) {
          // Would need to implement file writing in the main process
          console.log("Would save to:", result.filePath);
        }
      } catch (error) {
        console.error("Failed to save file:", error);
      }
    } else {
      // In browser, use download link
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
    }
  }

  validateBatchSize(items: BatchItem[]): {
    isValid: boolean;
    message?: string;
  } {
    const MAX_BATCH_SIZE = 50; // Reasonable limit to prevent API abuse

    if (items.length === 0) {
      return { isValid: false, message: "No items to process" };
    }

    if (items.length > MAX_BATCH_SIZE) {
      return {
        isValid: false,
        message: `Batch size too large. Maximum ${MAX_BATCH_SIZE} items allowed.`,
      };
    }

    const emptyItems = items.filter((item) => !item.originalPrompt.trim());
    if (emptyItems.length > 0) {
      return {
        isValid: false,
        message: `${emptyItems.length} items have empty prompts`,
      };
    }

    return { isValid: true };
  }

  getProcessingStats(items: BatchItem[]): {
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    successRate: number;
  } {
    const total = items.length;
    const pending = items.filter((item) => item.status === "pending").length;
    const processing = items.filter(
      (item) => item.status === "processing"
    ).length;
    const completed = items.filter(
      (item) => item.status === "completed"
    ).length;
    const failed = items.filter((item) => item.status === "error").length;

    const processedItems = completed + failed;
    const successRate =
      processedItems > 0 ? (completed / processedItems) * 100 : 0;

    return {
      total,
      pending,
      processing,
      completed,
      failed,
      successRate,
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // Utility method to estimate processing time
  estimateProcessingTime(itemCount: number): string {
    const avgTimePerItem = 3; // seconds
    const totalSeconds = itemCount * avgTimePerItem;

    if (totalSeconds < 60) {
      return `~${totalSeconds} seconds`;
    } else if (totalSeconds < 3600) {
      const minutes = Math.ceil(totalSeconds / 60);
      return `~${minutes} minutes`;
    } else {
      const hours = Math.ceil(totalSeconds / 3600);
      return `~${hours} hours`;
    }
  }
}

export const batchService = new BatchService();
