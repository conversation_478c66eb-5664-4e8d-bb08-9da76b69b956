import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
import { ElectronAPI } from "../src/types"; // Import the ElectronAPI interface

// Define the API that will be exposed to the renderer process
const electronAPI: ElectronAPI = {
  // API key management
  storeApiKey: (apiKey: string) => ipcRenderer.invoke("store-api-key", apiKey),
  getApiKey: () => ipcRenderer.invoke("get-api-key"),

  // Model selection management
  storeSelectedModel: (modelId: string) =>
    ipcRenderer.invoke("store-selected-model", modelId),
  getSelectedModel: () => ipcRenderer.invoke("get-selected-model"),

  // File operations
  showSaveDialog: () => ipcRenderer.invoke("show-save-dialog"),
  showOpenDialog: () => ipcRenderer.invoke("show-open-dialog"),

  // Clipboard operations
  clipboardWriteText: (text: string) =>
    ipcRenderer.invoke("clipboard-write-text", text),
  clipboardReadText: () => ipc<PERSON>enderer.invoke("clipboard-read-text"),

  // System notifications
  showNotification: (options: {
    title: string;
    body: string;
    silent?: boolean;
  }) => ipcRenderer.invoke("show-notification", options),

  // External links
  openExternal: (url: string) => ipcRenderer.invoke("open-external", url),

  // Window management
  minimizeToTray: () => ipcRenderer.invoke("minimize-to-tray"),
  showWindow: () => ipcRenderer.invoke("show-window"),

  // Event listeners
  onQuickEnhanceClipboard: (callback: (text: string) => void) => {
    ipcRenderer.on("quick-enhance-clipboard", (_, text) => callback(text));
  },
  onOpenSettings: (callback: () => void) => {
    ipcRenderer.on("open-settings", () => callback());
  },

  // Platform info
  platform: process.platform,
  isDev: () => ipcRenderer.invoke("is-dev"),
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld("electronAPI", electronAPI);
