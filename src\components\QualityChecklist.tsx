import { FC, useMemo } from "react";
import { EnhancementMode, QualityCheckItem } from "../types";

interface QualityChecklistProps {
  mode: EnhancementMode;
  originalPrompt: string;
  enhancedPrompt: string;
  className?: string;
}

export const QualityChecklist: FC<QualityChecklistProps> = ({
  mode,
  originalPrompt,
  enhancedPrompt,
  className = "",
}) => {
  const checklistItems = useMemo(() => {
    const baseItems: QualityCheckItem[] = [
      {
        id: "clarity",
        label: "Clear and Specific",
        description: "Prompt is clear, specific, and unambiguous",
        isRequired: true,
        status: "unchecked",
        validator: (prompt) => prompt.length > 20 && !prompt.includes("..."),
      },
      {
        id: "context",
        label: "Sufficient Context",
        description: "Provides enough background information",
        isRequired: true,
        status: "unchecked",
        validator: (prompt) => prompt.length > 50,
      },
      {
        id: "actionable",
        label: "Actionable Instructions",
        description: "Contains clear, actionable instructions",
        isRequired: true,
        status: "unchecked",
        validator: (prompt) =>
          /\b(create|generate|write|analyze|explain|describe|list|compare)\b/i.test(
            prompt
          ),
      },
    ];

    // Add mode-specific items
    switch (mode) {
      case "structured":
        baseItems.push(
          {
            id: "role_defined",
            label: "Role Defined",
            description: "Clear role or persona is specified",
            isRequired: true,
            status: "unchecked",
            validator: (prompt) =>
              /\b(you are|act as|role|persona)\b/i.test(prompt),
          },
          {
            id: "output_format",
            label: "Output Format Specified",
            description: "Desired output format is clearly defined",
            isRequired: false,
            status: "unchecked",
            validator: (prompt) =>
              /\b(format|structure|output|response)\b/i.test(prompt),
          }
        );
        break;
      case "template":
        baseItems.push(
          {
            id: "template_structure",
            label: "Template Structure",
            description: "Follows a consistent template structure",
            isRequired: true,
            status: "unchecked",
            validator: (prompt) =>
              prompt.includes("##") || prompt.includes("**"),
          },
          {
            id: "placeholders",
            label: "Placeholders Present",
            description: "Contains placeholders for customization",
            isRequired: false,
            status: "unchecked",
            validator: (prompt) => /\[.*\]|\{.*\}/.test(prompt),
          }
        );
        break;
      case "batch":
        baseItems.push({
          id: "consistency",
          label: "Consistent Format",
          description: "All prompts follow consistent formatting",
          isRequired: true,
          status: "unchecked",
          validator: () => true, // Would need batch context
        });
        break;
    }

    // Add enhancement-specific checks
    if (enhancedPrompt) {
      baseItems.push(
        {
          id: "improvement",
          label: "Meaningful Enhancement",
          description: "Enhanced version shows clear improvements",
          isRequired: true,
          status: "unchecked",
          validator: (prompt: string, enhancedPrompt?: string): boolean => {
            return Boolean(
              enhancedPrompt && enhancedPrompt.length > prompt.length * 1.2
            );
          },
        },
        {
          id: "preservation",
          label: "Core Intent Preserved",
          description: "Original intent and meaning are preserved",
          isRequired: true,
          status: "unchecked",
          validator: (): boolean => true, // Would need semantic analysis
        }
      );
    }

    return baseItems;
  }, [mode, enhancedPrompt]);

  const evaluatedItems = useMemo(() => {
    return checklistItems.map((item) => {
      let status: QualityCheckItem["status"] = "unchecked";

      if (item.validator) {
        try {
          const isValid = item.validator(originalPrompt, enhancedPrompt);
          status = isValid ? "checked" : item.isRequired ? "error" : "warning";
        } catch {
          status = "error";
        }
      }

      return { ...item, status };
    });
  }, [checklistItems, originalPrompt, enhancedPrompt]);

  const getStatusIcon = (status: QualityCheckItem["status"]) => {
    switch (status) {
      case "checked":
        return "✓";
      case "warning":
        return "⚠";
      case "error":
        return "✗";
      default:
        return "○";
    }
  };

  const getStatusColor = (status: QualityCheckItem["status"]) => {
    switch (status) {
      case "checked":
        return "text-green-600";
      case "warning":
        return "text-yellow-600";
      case "error":
        return "text-red-600";
      default:
        return "text-gray-400";
    }
  };

  const stats = useMemo(() => {
    const total = evaluatedItems.length;
    const checked = evaluatedItems.filter(
      (item) => item.status === "checked"
    ).length;
    const warnings = evaluatedItems.filter(
      (item) => item.status === "warning"
    ).length;
    const errors = evaluatedItems.filter(
      (item) => item.status === "error"
    ).length;

    return { total, checked, warnings, errors };
  }, [evaluatedItems]);

  return (
    <div
      className={`bg-white rounded-lg overflow-auto shadow-sm border border-gray-200 ${className}`}
    >
      <div className="px-4 py-3 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          Quality Checklist
        </h3>
        <div className="mt-2 flex items-center space-x-4 text-sm">
          <span className="text-green-600">✓ {stats.checked}</span>
          {stats.warnings > 0 && (
            <span className="text-yellow-600">⚠ {stats.warnings}</span>
          )}
          {stats.errors > 0 && (
            <span className="text-red-600">✗ {stats.errors}</span>
          )}
          <span className="text-gray-500">
            ({stats.checked}/{stats.total})
          </span>
        </div>
      </div>

      <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
        {evaluatedItems.map((item) => (
          <div key={item.id} className="flex items-start space-x-3">
            <span className={`text-lg ${getStatusColor(item.status)} mt-0.5`}>
              {getStatusIcon(item.status)}
            </span>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <h4 className="text-sm font-medium text-gray-900">
                  {item.label}
                </h4>
                {item.isRequired && (
                  <span className="text-xs text-red-500 font-medium">
                    Required
                  </span>
                )}
              </div>
              <p className="text-xs text-gray-600 mt-1">{item.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
