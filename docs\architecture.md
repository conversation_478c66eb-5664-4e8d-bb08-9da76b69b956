# Application Architecture

This document outlines the high-level architecture of the Prompt Enhancer Desktop App, detailing its core components, data flow, and key technologies.

## 1. Core Components

The application is structured around a clear separation of concerns, leveraging Electron's multi-process architecture:

- **Frontend (Electron Renderer Process)**:

  - Built with React and TypeScript.
  - Responsible for the user interface, user interactions, and rendering of application content.
  - Communicates with the Electron Main Process via Inter-Process Communication (IPC) for system-level operations and AI integration.

- **Backend (Electron Main Process)**:

  - Handles system-level integrations, such as:
    - Managing the application window.
    - System tray integration.
    - Global keyboard shortcuts.
    - Clipboard access.
    - Secure storage of sensitive data (e.g., API keys).
  - Acts as an intermediary between the Frontend and external services (like the Gemini AI API) to ensure security and proper resource management.

- **AI Integration (Service Layer)**:

  - A dedicated service layer (`src/services/geminiService.ts`) within the renderer process.
  - Manages communication with the Google Gemini AI API.
  - Handles API key authentication, request formatting, response parsing, error handling, and rate limiting.

- **Storage (Local JSON-based)**:
  - Utilizes local storage for persistence of user data.
  - Stores prompt history, custom templates, and application settings (e.g., API key, selected model).
  - Data is typically stored in JSON files within Electron's `app.getPath('userData')` directory.

## 2. Data Flow

The typical data flow for prompt enhancement is as follows:

1.  **User Input**: The user enters or loads a prompt into the input editor in the Frontend.
2.  **Mode Processing**: The application processes the input based on the currently selected enhancement mode (e.g., Quick, Structured, Template, Batch). For structured prompts, sections are combined into a single input string.
3.  **API Request**: The Frontend's `geminiService` constructs a request to the Gemini AI API with the processed prompt and selected enhancement style. This request is often routed through the Electron Main Process for security and system resource management.
4.  **AI Processing**: The Gemini AI API processes the request and returns an enhanced version of the prompt.
5.  **Result Display**: The enhanced prompt is received by the Frontend and displayed in the output editor, often with a diff view highlighting changes from the original.
6.  **User Actions**: The user can then copy the enhanced prompt, save it to history, or further modify it.

## 3. Key Technologies

- **Electron**: Provides the framework for building cross-platform desktop applications using web technologies.
- **React**: A declarative JavaScript library for building user interfaces, used for the Frontend.
- **TypeScript**: A superset of JavaScript that adds static typing, improving code quality and maintainability across the entire codebase.
- **Tailwind CSS**: A utility-first CSS framework used for styling the application, enabling rapid UI development.
- **Monaco Editor**: The code editor that powers VS Code, integrated into the application for advanced text editing capabilities.
- **diff2html / diff-match-patch**: Libraries used for visualizing differences between the original and enhanced prompts, providing a clear comparison.
- **Google Gemini AI API**: The core AI service used for prompt enhancement.
