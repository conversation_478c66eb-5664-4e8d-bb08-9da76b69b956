import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { HistoryService } from "../historyService";
import { PromptHistory, EnhancementMode, EnhancementStyle } from "../../types";

describe("HistoryService", () => {
  let service: HistoryService;
  const MOCK_STORAGE_KEY = "prompt-enhancer-history";
  const MOCK_MAX_HISTORY_ITEMS = 100;

  // Mock localStorage
  const localStorageMock = (() => {
    let store: { [key: string]: string } = {};
    return {
      getItem: (key: string) => store[key] || null,
      setItem: (key: string, value: string) => {
        store[key] = value.toString();
      },
      clear: () => {
        store = {};
      },
      removeItem: (key: string) => {
        delete store[key];
      },
    };
  })();

  Object.defineProperty(window, "localStorage", { value: localStorageMock });

  beforeEach(() => {
    localStorage.clear();
    service = new HistoryService();
    // Reset the private properties for consistent testing
    (service as any).STORAGE_KEY = MOCK_STORAGE_KEY;
    (service as any).MAX_HISTORY_ITEMS = MOCK_MAX_HISTORY_ITEMS;
    let idCounter = 0;
    vi.spyOn(service as any, "generateId").mockImplementation(
      () => `mock-id-${++idCounter}`
    );
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("saveToHistory", () => {
    it("should save a new history item", async () => {
      const originalPrompt = "Test original";
      const enhancedPrompt = "Test enhanced";
      const mode: EnhancementMode = "quick";
      const style: EnhancementStyle = "concise";

      const savedItem = await service.saveToHistory(
        originalPrompt,
        enhancedPrompt,
        mode,
        style
      );

      expect(savedItem).toBeDefined();
      expect(savedItem.originalPrompt).toBe(originalPrompt);
      expect(savedItem.enhancedPrompt).toBe(enhancedPrompt);
      expect(savedItem.mode).toBe(mode);
      expect(savedItem.style).toBe(style);
      expect(savedItem.id).toBe("mock-id-1");
      expect(savedItem.timestamp).toBeInstanceOf(Date);

      const history = await service.getHistory();
      expect(history).toHaveLength(1);
      expect(history[0]).toEqual(savedItem);
    });

    it("should add new items to the beginning of the history", async () => {
      await service.saveToHistory("P1", "E1", "quick", "concise");
      await service.saveToHistory("P2", "E2", "structured", "creative");

      const history = await service.getHistory();
      expect(history).toHaveLength(2);
      expect(history[0].originalPrompt).toBe("P2");
      expect(history[1].originalPrompt).toBe("P1");
    });

    it("should respect MAX_HISTORY_ITEMS limit", async () => {
      // Fill history up to MAX_HISTORY_ITEMS
      for (let i = 0; i < MOCK_MAX_HISTORY_ITEMS; i++) {
        await service.saveToHistory(`P${i}`, `E${i}`, "quick", "concise");
      }
      expect(await service.getHistory()).toHaveLength(MOCK_MAX_HISTORY_ITEMS);

      // Add one more item, expecting the oldest to be removed
      await service.saveToHistory(
        "Newest",
        "Newest Enhanced",
        "quick",
        "concise"
      );
      const history = await service.getHistory();
      expect(history).toHaveLength(MOCK_MAX_HISTORY_ITEMS);
      expect(history[0].originalPrompt).toBe("Newest");
      expect(history[MOCK_MAX_HISTORY_ITEMS - 1].originalPrompt).toBe(`P1`); // P0 was removed
    });

    it("should save optional fields", async () => {
      const options = {
        category: "test-category",
        tags: ["tag1", "tag2"],
        rating: 5,
        notes: "Some notes",
      };
      const savedItem = await service.saveToHistory(
        "P",
        "E",
        "quick",
        "concise",
        options
      );
      expect(savedItem.category).toBe(options.category);
      expect(savedItem.tags).toEqual(options.tags);
      expect(savedItem.rating).toBe(options.rating);
      expect(savedItem.notes).toBe(options.notes);
    });
  });

  describe("getHistory", () => {
    it("should return an empty array if no history exists", async () => {
      const history = await service.getHistory();
      expect(history).toEqual([]);
    });

    it("should retrieve saved history items", async () => {
      const item1 = await service.saveToHistory("P1", "E1", "quick", "concise");
      const item2 = await service.saveToHistory(
        "P2",
        "E2",
        "structured",
        "creative"
      );

      const history = await service.getHistory();
      expect(history).toHaveLength(2);
      // Ensure timestamps are correctly converted back to Date objects
      expect(history[0].timestamp).toBeInstanceOf(Date);
      expect(history[1].timestamp).toBeInstanceOf(Date);
      // Compare without direct object equality due to Date object
      expect(history[0].id).toBe(item2.id);
      expect(history[1].id).toBe(item1.id);
    });

    it("should handle invalid JSON in localStorage gracefully", async () => {
      localStorage.setItem(MOCK_STORAGE_KEY, "invalid json");
      const history = await service.getHistory();
      expect(history).toEqual([]);
    });
  });

  describe("deleteHistoryItem", () => {
    it("should delete a specific history item", async () => {
      const item1 = await service.saveToHistory("P1", "E1", "quick", "concise");
      const item2 = await service.saveToHistory("P2", "E2", "quick", "concise");

      await service.deleteHistoryItem(item1.id);
      const history = await service.getHistory();
      expect(history).toHaveLength(1);
      expect(history[0].id).toBe(item2.id);
    });

    it("should do nothing if item to delete does not exist", async () => {
      await service.saveToHistory("P1", "E1", "quick", "concise");
      await service.deleteHistoryItem("non-existent-id");
      const history = await service.getHistory();
      expect(history).toHaveLength(1);
    });
  });

  describe("clearHistory", () => {
    it("should clear all history items", async () => {
      await service.saveToHistory("P1", "E1", "quick", "concise");
      await service.saveToHistory("P2", "E2", "quick", "concise");
      expect(await service.getHistory()).toHaveLength(2);

      await service.clearHistory();
      expect(await service.getHistory()).toHaveLength(0);
    });
  });

  describe("updateHistoryItem", () => {
    it("should update an existing history item", async () => {
      const item = await service.saveToHistory("P1", "E1", "quick", "concise");
      const updates = { notes: "Updated notes", rating: 4 };

      await service.updateHistoryItem(item.id, updates);
      const history = await service.getHistory();
      const updatedItem = history.find((i) => i.id === item.id);

      expect(updatedItem?.notes).toBe(updates.notes);
      expect(updatedItem?.rating).toBe(updates.rating);
      expect(updatedItem?.originalPrompt).toBe(item.originalPrompt); // Ensure other fields are unchanged
    });

    it("should not update if item does not exist", async () => {
      await service.saveToHistory("P1", "E1", "quick", "concise");
      const initialHistory = await service.getHistory();

      await service.updateHistoryItem("non-existent-id", {
        notes: "New notes",
      });
      const currentHistory = await service.getHistory();
      expect(currentHistory).toEqual(initialHistory); // History should remain unchanged
    });
  });

  describe("exportHistory", () => {
    beforeEach(async () => {
      await service.saveToHistory("P1", "E1", "quick", "concise", {
        rating: 3,
        tags: ["tagA"],
      });
      await service.saveToHistory("P2", "E2", "structured", "creative", {
        notes: "Note B",
      });
    });

    it("should export history as JSON by default", async () => {
      const json = await service.exportHistory();
      const parsed = JSON.parse(json);
      expect(parsed).toHaveLength(2);
      expect(parsed[0].originalPrompt).toBe("P2");
      expect(parsed[1].originalPrompt).toBe("P1");
    });

    it("should export history as JSON when format is specified", async () => {
      const json = await service.exportHistory("json");
      const parsed = JSON.parse(json);
      expect(parsed).toHaveLength(2);
    });

    it("should export history as CSV", async () => {
      const csv = await service.exportHistory("csv");
      const lines = csv.split("\n");
      expect(lines).toHaveLength(3); // Header + 2 items
      expect(lines[0]).toBe(
        "ID,Timestamp,Mode,Style,Category,Tags,Rating,Original Prompt,Enhanced Prompt,Notes"
      );
      expect(lines[1]).toContain("P2");
      expect(lines[1]).toContain("E2");
      expect(lines[1]).toContain("structured");
      expect(lines[1]).toContain("creative");
      expect(lines[1]).toContain("Note B");
      expect(lines[2]).toContain("P1");
      expect(lines[2]).toContain("E1");
      expect(lines[2]).toContain("quick");
      expect(lines[2]).toContain("concise");
      expect(lines[2]).toContain("tagA");
      expect(lines[2]).toContain("3");
    });

    it("should handle quotes in CSV export", async () => {
      await service.saveToHistory(
        'Prompt with "quotes"',
        'Enhanced with "quotes"',
        "quick",
        "concise"
      );
      const csv = await service.exportHistory("csv");
      expect(csv).toContain('"Prompt with ""quotes"""');
      expect(csv).toContain('"Enhanced with ""quotes"""');
    });
  });

  describe("importHistory", () => {
    it("should import history from JSON data", async () => {
      const mockHistory: PromptHistory[] = [
        {
          id: "old-id-1",
          timestamp: new Date(),
          originalPrompt: "Imported P1",
          enhancedPrompt: "Imported E1",
          mode: "quick",
          style: "concise",
        },
        {
          id: "old-id-2",
          timestamp: new Date(),
          originalPrompt: "Imported P2",
          enhancedPrompt: "Imported E2",
          mode: "structured",
          style: "detailed",
        },
      ];
      const json = JSON.stringify(mockHistory);

      const importedCount = await service.importHistory(json, "json");
      expect(importedCount).toBe(2);

      const history = await service.getHistory();
      expect(history).toHaveLength(2);
      expect(history[0].originalPrompt).toBe("Imported P1");
      expect(history[1].originalPrompt).toBe("Imported P2");
      expect(history[0].id).not.toBe("old-id-2"); // New ID generated
    });

    it("should merge imported history with existing history", async () => {
      await service.saveToHistory(
        "Existing P1",
        "Existing E1",
        "quick",
        "concise"
      );

      const mockHistory: PromptHistory[] = [
        {
          id: "old-id-1",
          timestamp: new Date(),
          originalPrompt: "Imported P1",
          enhancedPrompt: "Imported E1",
          mode: "quick",
          style: "concise",
        },
      ];
      const json = JSON.stringify(mockHistory);

      const importedCount = await service.importHistory(json, "json");
      expect(importedCount).toBe(1);

      const history = await service.getHistory();
      expect(history).toHaveLength(2);
      expect(history[0].originalPrompt).toBe("Imported P1");
      expect(history[1].originalPrompt).toBe("Existing P1");
    });

    it("should throw error for unsupported CSV import", async () => {
      await expect(service.importHistory("csv data", "csv")).rejects.toThrow(
        "CSV import not yet implemented"
      );
    });

    it("should handle invalid JSON during import", async () => {
      await expect(
        service.importHistory("invalid json", "json")
      ).rejects.toThrow("Invalid history data format");
    });

    it("should filter out invalid history items during import", async () => {
      const mockHistory = [
        {
          id: "valid-1",
          timestamp: new Date().toISOString(),
          originalPrompt: "P1",
          enhancedPrompt: "E1",
          mode: "quick",
          style: "concise",
        },
        { id: "invalid-1", originalPrompt: "P2" }, // Missing fields
      ];
      const json = JSON.stringify(mockHistory);

      const importedCount = await service.importHistory(json, "json");
      expect(importedCount).toBe(1);
      const history = await service.getHistory();
      expect(history).toHaveLength(1);
      expect(history[0].originalPrompt).toBe("P1");
    });
  });

  describe("getStatistics", () => {
    it("should return default statistics for empty history", async () => {
      const stats = await service.getStatistics();
      expect(stats.totalItems).toBe(0);
      expect(stats.modeDistribution).toEqual({});
      expect(stats.styleDistribution).toEqual({});
      expect(stats.averageRating).toBe(0);
      expect(stats.recentActivity).toHaveLength(7);
      stats.recentActivity.forEach(
        (activity: { date: string; count: number }) =>
          expect(activity.count).toBe(0)
      );
    });

    it("should calculate correct statistics for populated history", async () => {
      vi.useFakeTimers();
      const now = new Date("2025-01-07T12:00:00Z");
      vi.setSystemTime(now);

      await service.saveToHistory(
        "P1",
        "E1",
        "quick" as EnhancementMode,
        "concise" as EnhancementStyle,
        {
          rating: 5,
        }
      );
      await service.saveToHistory(
        "P2",
        "E2",
        "structured" as EnhancementMode,
        "creative" as EnhancementStyle,
        {
          rating: 3,
        }
      );
      vi.setSystemTime(new Date("2025-01-06T12:00:00Z")); // One day before
      await service.saveToHistory(
        "P3",
        "E3",
        "quick" as EnhancementMode,
        "technical" as EnhancementStyle
      );
      vi.setSystemTime(new Date("2025-01-05T12:00:00Z")); // Two days before
      await service.saveToHistory(
        "P4",
        "E4",
        "batch" as EnhancementMode,
        "detailed" as EnhancementStyle
      );
      vi.setSystemTime(now); // Reset to current time

      const stats = await service.getStatistics();
      expect(stats.totalItems).toBe(4);
      expect(stats.modeDistribution).toEqual({
        quick: 2,
        structured: 1,
        batch: 1,
      });
      expect(stats.styleDistribution).toEqual({
        concise: 1,
        creative: 1,
        technical: 1,
        detailed: 1,
      });
      expect(stats.averageRating).toBe((5 + 3) / 2); // Only rated items
      expect(stats.recentActivity).toHaveLength(7);

      // Check recent activity counts for the last 3 days (Jan 5, 6, 7)
      const activityMap = new Map(
        stats.recentActivity.map((a: { date: string; count: number }) => [
          a.date,
          a.count,
        ])
      );
      expect(activityMap.get("2025-01-07")).toBe(2);
      expect(activityMap.get("2025-01-06")).toBe(1);
      expect(activityMap.get("2025-01-05")).toBe(1);
      expect(activityMap.get("2025-01-04")).toBe(0);
      vi.useRealTimers();
    });
  });

  describe("generateId", () => {
    it("should generate a unique ID", () => {
      vi.spyOn(Math, "random").mockReturnValue(0.123456789);
      vi.spyOn(Date, "now").mockReturnValue(1678886400000); // Fixed timestamp
      const id = (service as any).generateId();
      expect(id).toBe("mock-id-1"); // Based on mock values
    });
  });

  describe("isValidHistoryItem", () => {
    it("should return true for a valid history item", () => {
      const item: PromptHistory = {
        id: "1",
        timestamp: new Date(),
        originalPrompt: "P",
        enhancedPrompt: "E",
        mode: "quick",
        style: "concise",
      };
      expect((service as any).isValidHistoryItem(item)).toBe(true);
    });

    it("should return true for a valid history item with string timestamp", () => {
      const item: PromptHistory = {
        id: "1",
        timestamp: new Date().toISOString() as any, // Simulate string from localStorage
        originalPrompt: "P",
        enhancedPrompt: "E",
        mode: "quick",
        style: "concise",
      };
      expect((service as any).isValidHistoryItem(item)).toBe(true);
    });

    it("should return false for an invalid history item (missing fields)", () => {
      const item = { id: "1", originalPrompt: "P" }; // Missing enhancedPrompt, mode, style, timestamp
      expect((service as any).isValidHistoryItem(item)).toBe(false);
    });

    it("should return false for an invalid history item (wrong types)", () => {
      const item: any = {
        id: 1, // number instead of string
        timestamp: new Date(),
        originalPrompt: "P",
        enhancedPrompt: "E",
        mode: "quick",
        style: "concise",
      };
      expect((service as any).isValidHistoryItem(item)).toBe(false);
    });
  });
});
