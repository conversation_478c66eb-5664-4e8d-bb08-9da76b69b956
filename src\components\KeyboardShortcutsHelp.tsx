import React from 'react';
import { getDefaultShortcuts, useShortcutHelp } from '../hooks/useKeyboardShortcuts';

interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
  isOpen,
  onClose,
}) => {
  const { formatShortcut } = useShortcutHelp();

  if (!isOpen) return null;

  const shortcuts = getDefaultShortcuts({
    enhance: () => {},
    copy: () => {},
    paste: () => {},
    clear: () => {},
    toggleDiff: () => {},
    toggleSidebar: () => {},
    openSettings: () => {},
    newPrompt: () => {},
    save: () => {},
    undo: () => {},
    redo: () => {},
    selectAll: () => {},
    find: () => {},
    minimizeToTray: () => {},
  });

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Keyboard Shortcuts
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="px-6 py-4 overflow-y-auto max-h-[60vh]">
          {shortcuts.map((group) => (
            <div key={group.name} className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                {group.name}
              </h3>
              <div className="space-y-2">
                {group.shortcuts.map((shortcut, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-md"
                  >
                    <span className="text-sm text-gray-700">
                      {shortcut.description}
                    </span>
                    <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-white border border-gray-300 rounded shadow-sm">
                      {formatShortcut(shortcut)}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          ))}

          <div className="mt-6 p-4 bg-blue-50 rounded-md">
            <h3 className="text-lg font-medium text-blue-900 mb-2">
              Global Shortcuts
            </h3>
            <p className="text-sm text-blue-800 mb-3">
              These shortcuts work even when the app is minimized to the system tray:
            </p>
            <div className="space-y-2">
              <div className="flex items-center justify-between py-1">
                <span className="text-sm text-blue-700">
                  Show/hide application
                </span>
                <kbd className="px-2 py-1 text-xs font-semibold text-blue-800 bg-white border border-blue-300 rounded shadow-sm">
                  Ctrl + Shift + P
                </kbd>
              </div>
              <div className="flex items-center justify-between py-1">
                <span className="text-sm text-blue-700">
                  Quick enhance from clipboard
                </span>
                <kbd className="px-2 py-1 text-xs font-semibold text-blue-800 bg-white border border-blue-300 rounded shadow-sm">
                  Ctrl + Shift + E
                </kbd>
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook for managing keyboard shortcuts help
export const useKeyboardShortcutsHelp = () => {
  const [isOpen, setIsOpen] = React.useState(false);

  const openHelp = () => setIsOpen(true);
  const closeHelp = () => setIsOpen(false);

  return {
    isOpen,
    openHelp,
    closeHelp,
    KeyboardShortcutsHelp: (props: Omit<KeyboardShortcutsHelpProps, 'isOpen' | 'onClose'>) => (
      <KeyboardShortcutsHelp {...props} isOpen={isOpen} onClose={closeHelp} />
    ),
  };
};
