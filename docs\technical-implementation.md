# Technical Implementation

This document details the technical aspects of the Prompt Enhancer Desktop App, covering the core technologies, API integration, storage, and system integration.

## 1. Core Technologies

The application is built upon a robust stack of modern web and desktop technologies:

- **Electron**: The foundational framework for building cross-platform desktop applications using web technologies (HTML, CSS, JavaScript). It manages the main and renderer processes, providing access to native OS functionalities.
- **React**: A popular JavaScript library for building user interfaces. It is used for the entire frontend of the application, enabling a component-based, declarative approach to UI development.
- **TypeScript**: A superset of JavaScript that adds static type definitions. It is used throughout the project to enhance code quality, readability, and maintainability by catching errors at compile-time.
- **Tailwind CSS**: A utility-first CSS framework that allows for rapid UI development by providing low-level utility classes directly in the markup. This approach ensures consistency and speeds up styling.
- **Monaco Editor**: The powerful code editor that powers Visual Studio Code. It is integrated into the application to provide advanced text editing features for prompt input and output, including syntax highlighting, auto-completion, and large file handling.
- **diff2html / diff-match-patch**: JavaScript libraries used for generating and visualizing differences between two text inputs. These are crucial for the "Diff View" feature, allowing users to easily see changes between original and enhanced prompts.

## 2. API Integration

The application's core functionality relies on seamless integration with Google's AI services:

- **Google Gemini AI API**:
  - **Authentication Handling**: Manages secure authentication with the Gemini API, typically involving API keys stored securely.
  - **Request Formatting**: Constructs API requests with appropriate parameters, including the prompt content, enhancement mode, and style.
  - **Response Parsing**: Processes and extracts the enhanced prompt from the API responses.
  - **Error Handling**: Implements robust error handling mechanisms to gracefully manage API errors, network issues, and rate limits.
  - **Rate Limiting Management**: Incorporates strategies (e.g., exponential backoff, debouncing) to prevent exceeding API rate limits and ensure smooth operation.

## 3. Storage Implementation

User data and application settings are persisted locally:

- **Local Storage (JSON Files)**:
  - **Mechanism**: Data is stored in JSON files within the application's user data directory, typically accessed via Electron's `app.getPath('userData')`. This ensures data persistence across application sessions.
  - **History Structure**: Stores a detailed history of original and enhanced prompts, along with their mode and style, enabling users to revisit past interactions.
  - **Template Storage**: Manages the saving and loading of custom prompt templates, allowing users to reuse predefined structures.
  - **Settings Persistence**: Persists application settings such as the Gemini API key, selected AI model, and UI layout preferences.

## 4. System Integration

The application leverages Electron's capabilities to integrate deeply with the operating system:

- **System Tray**:

  - **Custom Icon**: Displays a custom icon in the system tray (Windows) or menu bar (macOS).
  - **Context Menu**: Provides a context menu for quick actions like showing/hiding the application, accessing settings, or initiating a quick enhance.
  - **Show/Hide Application**: Allows users to minimize the application to the tray and restore it easily.

- **Clipboard**:

  - **Copy Functionality**: Enables one-click copying of enhanced prompts to the system clipboard.
  - **Format Preservation**: Ensures that copied content retains its formatting where applicable.
  - **Quick Enhance from Clipboard**: A global shortcut allows users to quickly enhance text directly from their clipboard without manually pasting it into the app.

- **Global Shortcuts**:
  - **Description**: Implements system-wide keyboard shortcuts for enhanced productivity.
  - **Examples**:
    - `Ctrl+Shift+P`: Toggles the visibility of the application window.
    - `Ctrl+Shift+E`: Triggers a quick enhancement using content from the clipboard.
    - `Ctrl+Enter`: Initiates the enhancement process for the current prompt in the editor.
    - `Ctrl+C`: Copies the enhanced prompt to the clipboard.
    - `Ctrl+V`: Pastes content from the clipboard into the input editor.
    - `Ctrl+D`: Toggles the diff view for comparing prompts.
    - `Ctrl+B`: Toggles the visibility of the right sidebar.
