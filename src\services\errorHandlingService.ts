/**
 * Error handling service with retry mechanisms and error categorization
 */

export interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryCondition?: (error: Error) => boolean;
}

export interface ErrorInfo {
  type: "network" | "api" | "validation" | "system" | "unknown";
  severity: "low" | "medium" | "high" | "critical";
  message: string;
  originalError: Error;
  timestamp: Date;
  context?: Record<string, any>;
  retryable: boolean;
}

export class ErrorHandlingService {
  private readonly DEFAULT_RETRY_OPTIONS: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
    retryCondition: (error) => this.isRetryableError(error),
  };

  private errorLog: ErrorInfo[] = [];
  private readonly MAX_ERROR_LOG_SIZE = 100;

  /**
   * Execute a function with retry logic
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<T> {
    const config = { ...this.DEFAULT_RETRY_OPTIONS, ...options };
    let lastError: Error;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Log the error
        this.logError(lastError, { attempt, maxRetries: config.maxRetries });

        // Check if we should retry
        if (
          attempt === config.maxRetries ||
          !config.retryCondition!(lastError)
        ) {
          throw lastError;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt),
          config.maxDelay
        );

        // Add jitter to prevent thundering herd
        const jitteredDelay = delay + Math.random() * 1000;

        await this.sleep(jitteredDelay);
      }
    }

    throw lastError!;
  }

  /**
   * Categorize and analyze errors
   */
  categorizeError(error: Error, context?: Record<string, any>): ErrorInfo {
    const errorInfo: ErrorInfo = {
      type: "unknown",
      severity: "medium",
      message: error.message,
      originalError: error,
      timestamp: new Date(),
      context,
      retryable: false,
    };

    // Network errors
    if (
      error.message.toLowerCase().includes("fetch") ||
      error.message.toLowerCase().includes("network") ||
      error.message.toLowerCase().includes("timeout") ||
      error.message.toLowerCase().includes("econnrefused") ||
      error.message.toLowerCase().includes("enotfound")
    ) {
      errorInfo.type = "network";
      errorInfo.severity = "medium";
      errorInfo.retryable = true;
    }
    // API errors
    else if (
      error.message.includes("API") ||
      error.message.includes("401") ||
      error.message.includes("403") ||
      error.message.includes("429") ||
      error.message.includes("500") ||
      error.message.includes("502") ||
      error.message.includes("503")
    ) {
      errorInfo.type = "api";
      errorInfo.severity =
        error.message.includes("401") || error.message.includes("403")
          ? "high"
          : "medium";
      errorInfo.retryable =
        !error.message.includes("401") && !error.message.includes("403");
    }
    // Validation errors
    else if (
      error.message.toLowerCase().includes("validation") ||
      error.message.toLowerCase().includes("invalid") ||
      error.message.toLowerCase().includes("required")
    ) {
      errorInfo.type = "validation";
      errorInfo.severity = "low";
      errorInfo.retryable = false;
    }
    // System errors
    else if (
      error.message.includes("permission") ||
      error.message.includes("access") ||
      error.message.includes("file") ||
      error.message.includes("disk")
    ) {
      errorInfo.type = "system";
      errorInfo.severity = "high";
      errorInfo.retryable = false;
    }

    return errorInfo;
  }

  /**
   * Log error with context
   */
  logError(error: Error, context?: Record<string, any>): void {
    const errorInfo = this.categorizeError(error, context);

    // Add to error log
    this.errorLog.push(errorInfo);

    // Keep log size manageable
    if (this.errorLog.length > this.MAX_ERROR_LOG_SIZE) {
      this.errorLog.splice(0, this.errorLog.length - this.MAX_ERROR_LOG_SIZE);
    }

    // Console logging based on severity
    switch (errorInfo.severity) {
      case "critical":
        console.error("CRITICAL ERROR:", errorInfo);
        break;
      case "high":
        console.error("HIGH SEVERITY ERROR:", errorInfo);
        break;
      case "medium":
        console.warn("MEDIUM SEVERITY ERROR:", errorInfo);
        break;
      case "low":
        console.info("LOW SEVERITY ERROR:", errorInfo);
        break;
    }
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const errorInfo = this.categorizeError(error);
    return errorInfo.retryable;
  }

  /**
   * Get user-friendly error message
   */
  getUserFriendlyMessage(error: Error): string {
    // Check for specific quota error message first
    if (
      error.message.includes("Selected model doesn't have a free quota tier")
    ) {
      return error.message; // Return the specific message as-is
    }

    const errorInfo = this.categorizeError(error);

    switch (errorInfo.type) {
      case "network":
        return "Network connection issue. Please check your internet connection and try again.";
      case "api":
        if (error.message.includes("401") || error.message.includes("403")) {
          return "Authentication failed. Please check your API key.";
        }
        if (error.message.includes("429")) {
          return "Rate limit exceeded. Please wait a moment and try again.";
        }
        return "API service is temporarily unavailable. Please try again later.";
      case "validation":
        return "Invalid input. Please check your prompt and try again.";
      case "system":
        return "System error occurred. Please restart the application.";
      default:
        return "An unexpected error occurred. Please try again.";
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    recentErrors: ErrorInfo[];
  } {
    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};

    this.errorLog.forEach((error) => {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
    });

    return {
      total: this.errorLog.length,
      byType,
      bySeverity,
      recentErrors: this.errorLog.slice(-10), // Last 10 errors
    };
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Create a safe wrapper for async operations
   */
  safeAsync<T>(
    operation: () => Promise<T>,
    fallback?: T,
    onError?: (error: Error) => void
  ): Promise<T | undefined> {
    return operation().catch((error) => {
      this.logError(error);
      if (onError) {
        onError(error);
      }
      return fallback;
    });
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Circuit breaker pattern implementation
   */
  createCircuitBreaker<T>(
    operation: () => Promise<T>,
    options: {
      failureThreshold: number;
      resetTimeout: number;
      monitoringPeriod: number;
    } = {
      failureThreshold: 5,
      resetTimeout: 60000,
      monitoringPeriod: 10000,
    }
  ) {
    let failures = 0;
    let lastFailureTime = 0;
    let state: "closed" | "open" | "half-open" = "closed";

    return async (): Promise<T> => {
      const now = Date.now();

      // Reset failures if monitoring period has passed
      if (now - lastFailureTime > options.monitoringPeriod) {
        failures = 0;
      }

      // Check circuit state
      if (state === "open") {
        if (now - lastFailureTime > options.resetTimeout) {
          state = "half-open";
        } else {
          throw new Error("Circuit breaker is open");
        }
      }

      try {
        const result = await operation();

        // Success - reset circuit
        if (state === "half-open") {
          state = "closed";
          failures = 0;
        }

        return result;
      } catch (error) {
        failures++;
        lastFailureTime = now;

        if (failures >= options.failureThreshold) {
          state = "open";
        }

        throw error;
      }
    };
  }
}

// Export singleton instance
export const errorHandlingService = new ErrorHandlingService();
