import { FC, useState, useRef } from "react";
import { BatchProcessingState, BatchItem, EnhancementStyle } from "../types";

interface BatchProcessorProps {
  batchState: BatchProcessingState;
  currentStyle: EnhancementStyle;
  onImportFile: (file: File) => void;
  onStartProcessing: () => void;
  onStopProcessing: () => void;
  onExportResults: () => void;
  onClearBatch: () => void;
  onRemoveItem: (id: string) => void;
  className?: string;
}

export const BatchProcessor: FC<BatchProcessorProps> = ({
  batchState,
  currentStyle,
  onImportFile,
  onStartProcessing,
  onStopProcessing,
  onExportResults,
  onClearBatch,
  onRemoveItem,
  className = "",
}) => {
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onImportFile(file);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    
    const file = event.dataTransfer.files[0];
    if (file && file.type === "text/plain") {
      onImportFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = () => {
    setDragOver(false);
  };

  const getStatusColor = (status: BatchItem["status"]) => {
    switch (status) {
      case "completed":
        return "text-green-600";
      case "processing":
        return "text-blue-600";
      case "error":
        return "text-red-600";
      default:
        return "text-gray-500";
    }
  };

  const getStatusIcon = (status: BatchItem["status"]) => {
    switch (status) {
      case "completed":
        return "✓";
      case "processing":
        return "⟳";
      case "error":
        return "✗";
      default:
        return "○";
    }
  };

  const progressPercentage = batchState.totalItems > 0 
    ? Math.round(((batchState.completedItems + batchState.failedItems) / batchState.totalItems) * 100)
    : 0;

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">Batch Processing</h3>
          <div className="flex items-center space-x-2">
            {batchState.items.length > 0 && (
              <button
                onClick={onClearBatch}
                className="text-xs text-red-600 hover:text-red-800"
                disabled={batchState.isProcessing}
              >
                Clear
              </button>
            )}
          </div>
        </div>

        {/* File Import */}
        <div
          className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
            dragOver
              ? "border-primary-400 bg-primary-50"
              : "border-gray-300 hover:border-gray-400"
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".txt"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          <div className="space-y-2">
            <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <div>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="text-primary-600 hover:text-primary-700 font-medium"
                disabled={batchState.isProcessing}
              >
                Choose file
              </button>
              <span className="text-gray-500"> or drag and drop</span>
            </div>
            <p className="text-xs text-gray-500">
              Upload a .txt file with one prompt per line
            </p>
          </div>
        </div>

        {/* Progress */}
        {batchState.totalItems > 0 && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-gray-600">
                Progress: {batchState.completedItems + batchState.failedItems} / {batchState.totalItems}
              </span>
              <span className="text-gray-600">{progressPercentage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            <div className="flex items-center justify-between text-xs mt-2 text-gray-500">
              <span>✓ {batchState.completedItems} completed</span>
              {batchState.failedItems > 0 && (
                <span className="text-red-600">✗ {batchState.failedItems} failed</span>
              )}
            </div>
          </div>
        )}

        {/* Controls */}
        {batchState.items.length > 0 && (
          <div className="flex items-center space-x-2 mt-4">
            {!batchState.isProcessing ? (
              <button
                onClick={onStartProcessing}
                className="px-3 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                Start Processing
              </button>
            ) : (
              <button
                onClick={onStopProcessing}
                className="px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Stop Processing
              </button>
            )}
            
            {batchState.completedItems > 0 && (
              <button
                onClick={onExportResults}
                className="px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                disabled={batchState.isProcessing}
              >
                Export Results
              </button>
            )}
          </div>
        )}
      </div>

      {/* Items List */}
      <div className="max-h-64 overflow-y-auto">
        {batchState.items.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <p className="text-sm">No items to process</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {batchState.items.map((item, index) => (
              <div key={item.id} className="p-3 hover:bg-gray-50 group">
                <div className="flex items-start space-x-3">
                  <span className={`text-sm font-medium ${getStatusColor(item.status)} mt-1`}>
                    {getStatusIcon(item.status)}
                  </span>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-gray-500">Item {index + 1}</span>
                      {!batchState.isProcessing && (
                        <button
                          onClick={() => onRemoveItem(item.id)}
                          className="opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 transition-opacity"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fillRule="evenodd"
                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-900 line-clamp-2 mb-1">
                      {item.originalPrompt}
                    </p>
                    
                    {item.error && (
                      <p className="text-xs text-red-600 mt-1">
                        Error: {item.error}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
