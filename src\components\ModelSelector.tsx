import { FC, useState, useEffect } from "react";
import { GeminiModel } from "../types";
import { geminiService } from "../services/geminiService";

interface ModelSelectorProps {
  selectedModel: string | null;
  onModelSelect: (modelId: string) => void;
  disabled?: boolean;
}

export const ModelSelector: FC<ModelSelectorProps> = ({
  selectedModel,
  onModelSelect,
  disabled = false,
}) => {
  const [models, setModels] = useState<GeminiModel[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    if (!geminiService.isInitialized()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await geminiService.listModels();
      // console.log("response: ", response);

      const filteredModels = response.models.filter((model) => {
        const modelId = model.baseModelId || model.name.replace("models/", "");

        if (model.version === "001") return false;

        if (
          !modelId.startsWith("gemini") ||
          model.displayName.includes("TTS")
        ) {
          return false;
        }

        const versionMatch = modelId.match(/gemini-(\d+\.\d+)/);
        if (versionMatch && versionMatch[1]) {
          const version = parseFloat(versionMatch[1]);
          if (version <= 2.0) {
            return false;
          }
        }
        return true;
      });

      setModels(filteredModels);

      // If no model is selected, select the first stable model
      if (!selectedModel && filteredModels.length > 0) {
        // Prefer stable models (non-preview, non-experimental)
        const stableModels = filteredModels.filter((model) => {
          const modelId =
            model.baseModelId || model.name.replace("models/", "");
          return (
            !modelId.includes("preview") &&
            !modelId.includes("exp") &&
            !modelId.includes("experimental")
          );
        });

        const modelToSelect =
          stableModels.length > 0 ? stableModels[0] : filteredModels[0];
        const modelId =
          modelToSelect.baseModelId ||
          modelToSelect.name.replace("models/", "");

        onModelSelect(modelId);
      }
    } catch (err) {
      console.error("Failed to load models:", err);
      setError(err instanceof Error ? err.message : "Failed to load models");
    } finally {
      setIsLoading(false);
    }
  };

  const handleModelChange = async (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const modelId = event.target.value;

    // Validate model before selecting
    const isValid = await geminiService.validateModel(modelId);
    if (!isValid) {
      setError(
        `Model ${modelId} is not available. Please select a different model.`
      );
      return;
    }

    setError(null);
    onModelSelect(modelId);
  };

  const getModelDisplayName = (model: GeminiModel) => {
    let modelId = model.name.replace("models/", "") || model.baseModelId;
    let displayName = modelId;
    let dateSuffix = "";

    displayName = displayName.replace(/^gemini-/, "Gemini ");
    displayName = displayName.replace(/-/g, " ");

    // Add the date suffix back if it existed
    if (dateSuffix) {
      return `${displayName} ${dateSuffix}`;
    }

    return displayName;
  };

  const getModelDescription = (model: GeminiModel) => {
    const tokenInfo = `${
      model.inputTokenLimit?.toLocaleString() || "N/A"
    } input tokens`;
    return model.description
      ? `${model.description} (${tokenInfo})`
      : tokenInfo;
  };

  if (error) {
    return (
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Model Selection
        </label>
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex items-center">
            <div className="text-red-600 text-sm">
              <strong>Error loading models:</strong> {error}
            </div>
            <button
              onClick={loadModels}
              className="ml-auto text-red-600 hover:text-red-800 text-sm underline"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Model Selection
      </label>
      <div className="relative">
        <select
          value={selectedModel || ""}
          onChange={handleModelChange}
          disabled={disabled || isLoading || models.length === 0}
          className="w-full px-3 py-2 pr-10 bg-white text-gray-900 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100 disabled:cursor-not-allowed appearance-none"
        >
          {isLoading ? (
            <option value="">Loading models...</option>
          ) : models.length === 0 ? (
            <option value="">No models available</option>
          ) : (
            <>
              <option value="">Select a model</option>
              {models.map((model) => {
                // Use baseModelId if available, otherwise extract from name
                const modelId =
                  model.baseModelId || model.name.replace("models/", "");
                return (
                  <option key={modelId} value={modelId}>
                    {getModelDisplayName(model)}
                  </option>
                );
              })}
            </>
          )}
        </select>

        {/* Custom chevron icon */}
        {!isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <svg
              className="w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        )}

        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
          </div>
        )}
      </div>

      {selectedModel && models.length > 0 && (
        <div className="mt-2 text-sm text-gray-600">
          {(() => {
            const model = models.find((m) => {
              const modelId = m.baseModelId || m.name.replace("models/", "");
              return modelId === selectedModel;
            });
            return model ? (
              <div>
                <div className="font-medium">{getModelDisplayName(model)}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {getModelDescription(model)}
                </div>
              </div>
            ) : null;
          })()}
        </div>
      )}
    </div>
  );
};
