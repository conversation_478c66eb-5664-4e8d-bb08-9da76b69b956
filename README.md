# Prompt Enhancer Desktop App

A desktop application built with Electron and React that enhances prompts using Google's Gemini AI API.

## 1. Application Architecture

### Core Components

- Frontend: Electron renderer process with React and TypeScript
- Backend: Electron main process handling system integration
- AI Integration: Service layer for Gemini API communication
- Storage: Local JSON-based storage for history and templates

### Data Flow

1. User inputs prompt in the editor
2. <PERSON><PERSON> processes input through selected enhancement mode
3. Request sent to Gemini API with appropriate parameters
4. Enhanced result displayed in split view with diff highlighting
5. User can copy, save, or further modify the result

## 2. Feature Breakdown

### Enhancement Modes

- **Quick Enhancement Mode**: Simple input/output for fast prompt improvements. Supports multiple style options (creative, concise, technical, detailed).
- **Structured Prompt Mode**: Split prompts into logical sections: Role, Context, Instructions, Goal, Constraints. Allows custom section creation.
- **Project Template Mode**: Generate structured templates for new projects with sections like Project Overview, Prerequisites, Setup, Implementation Tasks. Customizable template structure.
- **Batch Processing Mode**: Import .txt files with multiple prompts, process and enhance all prompts at once, and export results in preferred format.

### Quality Assurance Features

- **Quality Checklist Sidebar**: Dynamic checklist based on selected mode with items like "includes persona", "specifies format". Visual indicators for met/unmet criteria.
- **Diff View**: Side-by-side comparison of original and enhanced prompts with highlighted changes using `diff2html` or `diff-match-patch`. Toggle between unified and split diff views.

### User Experience Features

- **System Tray Integration**: Quick access icon in system tray, configurable keyboard shortcuts for common actions, minimize to tray option.
- **Clipboard Integration**: One-click copy to clipboard, optional auto-copy on completion, keyboard shortcuts (Ctrl+C, Ctrl+Shift+C).
- **History Management**: Offline storage of prompt history, categorization and search functionality, export/import history.

### Advanced Features

- **Context Enhancement**: Input fields for project domain, audience, output format, with context-aware suggestions.
- **Two-Stage Enhancement**: (Planned) First pass to identify missing information, user fills gaps, second pass to generate final enhanced prompt.

## 3. User Interface Design

### Main Layout

- **Left Sidebar**: Mode selection, history access, settings.
- **Main Editor Area**: Input editor (top), output editor (bottom), diff view toggle.
- **Right Sidebar**: Quality checklist, enhancement options, context inputs.

### Modal Dialogs

- **Settings Dialog**: API key configuration, theme selection, keyboard shortcuts.
- **Batch Processing Dialog**: File selection, processing options, progress indicator.

### Responsive Considerations

- Collapsible sidebars for smaller screens.
- Minimum window size constraints.
- Scalable UI elements.

## 4. Technical Implementation

### Core Technologies

- **Electron**: Desktop application framework.
- **React**: UI library.
- **TypeScript**: Type-safe JavaScript.
- **Tailwind CSS**: Styling.
- **Monaco Editor**: Code/text editor component.
- **diff2html/diff-match-patch**: Diff visualization.

### API Integration

- **Gemini AI API**: Authentication handling, request formatting, response parsing, error handling, rate limiting management.

### Storage Implementation

- **Local Storage**: JSON files in `app.getPath('userData')` for history, template storage, and settings persistence.

### System Integration

- **System Tray**: Custom icon, context menu, show/hide application.
- **Clipboard**: Copy functionality, format preservation.
- **Global Shortcuts**: `Ctrl+Shift+P` (show/hide), `Ctrl+Shift+E` (quick enhance from clipboard), `Ctrl+Enter` (enhance current prompt), `Ctrl+C` (copy enhanced prompt), `Ctrl+V` (paste from clipboard), `Ctrl+D` (toggle diff view), `Ctrl+B` (toggle sidebar).

## 5. Development Roadmap

This project follows a phased development approach:

- **Phase 1: Core Functionality ✅**

  - Basic Electron app setup
  - Editor implementation with Monaco Editor
  - Gemini API integration
  - Simple enhancement mode with multiple styles

- **Phase 2: Enhancement Modes ✅**

  - Structured Prompt Builder with customizable sections
  - Template Generator with reusable templates
  - Project Template Mode with structured outputs

- **Phase 3: Advanced Features ✅**

  - Diff view for comparing original and enhanced prompts
  - Quality checklist with dynamic validation
  - History management with search and categorization
  - Batch processing for multiple prompts

- **Phase 4: Polish & Optimization ✅**
  - System tray integration with context menu
  - Global keyboard shortcuts
  - Enhanced clipboard integration with system notifications
  - Performance optimizations with caching and debouncing
  - Advanced error handling with retry mechanisms
  - Toast notifications for better user feedback
  - React Error Boundaries for graceful error recovery

## 6. Technical Considerations

### Performance

- Optimize API calls with debouncing.
- Efficient diff calculation.
- Lazy loading of non-critical components.

### Security

- Secure API key storage.
- Input validation.
- Safe file handling.

### Error Handling

- Graceful API failure recovery.
- Informative error messages.
- Automatic retry mechanisms.

### Configuration

- User-configurable API parameters.
- Temperature settings (0.2-0.5 recommended).
- Model selection options.

## 7. Testing Strategy

- Unit tests for core functionality.
- Integration tests for API communication.
- End-to-end tests for user flows.
- Manual testing for UI/UX.

## 8. Deployment & Distribution

- Electron builder configuration.
- Auto-update mechanism.
- Platform-specific builds (Windows, macOS, Linux).
- Installation instructions.

---

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Google Gemini API key

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd prompt-enhancer
```

2. Install dependencies:

```bash
npm install
```

3. Get your Gemini API key:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Sign in with your Google account
   - Create an API key
   - Copy the generated key

## Development

Run the development server:

```bash
npm run dev
```

This will start both the React development server and the Electron app.

## Building

Build the application:

```bash
npm run build
```

Package for distribution:

```bash
# For current platform
npm run package

# For specific platforms
npm run package:win
npm run package:mac
npm run package:linux
```

## Usage

1. Launch the application
2. Enter your Gemini API key when prompted
3. Select an enhancement mode and style
4. Enter your prompt in the input editor
5. Click "Enhance Prompt" to get an improved version
6. Copy the result or use it as input for further enhancement

## Project Structure

```
prompt-enhancer/
├── electron/           # Electron main process files
│   ├── main.ts        # Main Electron process
│   ├── preload.ts     # Preload script for IPC
│   └── utils.ts       # Utility functions
├── src/               # React application
│   ├── components/    # React components
│   ├── services/      # API services
│   ├── types/         # TypeScript definitions
│   └── App.tsx        # Main app component
├── dist/              # Built files
└── release/           # Packaged applications
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
