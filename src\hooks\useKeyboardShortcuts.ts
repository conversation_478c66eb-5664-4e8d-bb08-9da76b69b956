import { useEffect, useCallback } from 'react';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  preventDefault?: boolean;
  stopPropagation?: boolean;
  enabled?: boolean;
}

export interface ShortcutGroup {
  name: string;
  shortcuts: KeyboardShortcut[];
}

/**
 * Hook for managing keyboard shortcuts
 */
export const useKeyboardShortcuts = (shortcuts: KeyboardShortcut[]) => {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      for (const shortcut of shortcuts) {
        // Skip if shortcut is disabled
        if (shortcut.enabled === false) {
          continue;
        }

        // Check if the key matches
        if (event.key.toLowerCase() !== shortcut.key.toLowerCase()) {
          continue;
        }

        // Check modifier keys
        const ctrlMatch = (shortcut.ctrlKey ?? false) === event.ctrlKey;
        const shiftMatch = (shortcut.shiftKey ?? false) === event.shiftKey;
        const altMatch = (shortcut.altKey ?? false) === event.altKey;
        const metaMatch = (shortcut.metaKey ?? false) === event.metaKey;

        if (ctrlMatch && shiftMatch && altMatch && metaMatch) {
          if (shortcut.preventDefault !== false) {
            event.preventDefault();
          }
          if (shortcut.stopPropagation !== false) {
            event.stopPropagation();
          }
          
          shortcut.action();
          break;
        }
      }
    },
    [shortcuts]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
};

/**
 * Default keyboard shortcuts for the application
 */
export const getDefaultShortcuts = (actions: {
  enhance: () => void;
  copy: () => void;
  paste: () => void;
  clear: () => void;
  toggleDiff: () => void;
  toggleSidebar: () => void;
  openSettings: () => void;
  newPrompt: () => void;
  save: () => void;
  undo: () => void;
  redo: () => void;
  selectAll: () => void;
  find: () => void;
  minimizeToTray: () => void;
}): ShortcutGroup[] => [
  {
    name: 'General',
    shortcuts: [
      {
        key: 'Enter',
        ctrlKey: true,
        action: actions.enhance,
        description: 'Enhance prompt',
      },
      {
        key: 'n',
        ctrlKey: true,
        action: actions.newPrompt,
        description: 'New prompt',
      },
      {
        key: 's',
        ctrlKey: true,
        action: actions.save,
        description: 'Save prompt',
      },
      {
        key: ',',
        ctrlKey: true,
        action: actions.openSettings,
        description: 'Open settings',
      },
      {
        key: 'm',
        ctrlKey: true,
        shiftKey: true,
        action: actions.minimizeToTray,
        description: 'Minimize to tray',
      },
    ],
  },
  {
    name: 'Editing',
    shortcuts: [
      {
        key: 'z',
        ctrlKey: true,
        action: actions.undo,
        description: 'Undo',
      },
      {
        key: 'y',
        ctrlKey: true,
        action: actions.redo,
        description: 'Redo',
      },
      {
        key: 'a',
        ctrlKey: true,
        action: actions.selectAll,
        description: 'Select all',
      },
      {
        key: 'f',
        ctrlKey: true,
        action: actions.find,
        description: 'Find',
      },
      {
        key: 'Delete',
        ctrlKey: true,
        action: actions.clear,
        description: 'Clear prompt',
      },
    ],
  },
  {
    name: 'Clipboard',
    shortcuts: [
      {
        key: 'c',
        ctrlKey: true,
        action: actions.copy,
        description: 'Copy enhanced prompt',
      },
      {
        key: 'v',
        ctrlKey: true,
        action: actions.paste,
        description: 'Paste from clipboard',
      },
    ],
  },
  {
    name: 'View',
    shortcuts: [
      {
        key: 'd',
        ctrlKey: true,
        action: actions.toggleDiff,
        description: 'Toggle diff view',
      },
      {
        key: 'b',
        ctrlKey: true,
        action: actions.toggleSidebar,
        description: 'Toggle sidebar',
      },
    ],
  },
];

/**
 * Hook for displaying keyboard shortcuts help
 */
export const useShortcutHelp = () => {
  const formatShortcut = (shortcut: KeyboardShortcut): string => {
    const parts: string[] = [];
    
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.metaKey) parts.push('Cmd');
    
    parts.push(shortcut.key.toUpperCase());
    
    return parts.join(' + ');
  };

  const getShortcutHelp = (groups: ShortcutGroup[]): string => {
    return groups
      .map(group => {
        const shortcuts = group.shortcuts
          .map(shortcut => `  ${formatShortcut(shortcut)}: ${shortcut.description}`)
          .join('\n');
        return `${group.name}:\n${shortcuts}`;
      })
      .join('\n\n');
  };

  return { formatShortcut, getShortcutHelp };
};

/**
 * Hook for managing context-sensitive shortcuts
 */
export const useContextualShortcuts = (
  context: string,
  shortcuts: Record<string, KeyboardShortcut[]>
) => {
  const contextShortcuts = shortcuts[context] || [];
  useKeyboardShortcuts(contextShortcuts);
};

/**
 * Utility to check if an element should receive keyboard events
 */
export const shouldHandleKeyboardEvent = (event: KeyboardEvent): boolean => {
  const target = event.target as HTMLElement;
  
  // Don't handle shortcuts when typing in input fields
  if (
    target.tagName === 'INPUT' ||
    target.tagName === 'TEXTAREA' ||
    target.contentEditable === 'true' ||
    target.closest('[contenteditable="true"]')
  ) {
    return false;
  }
  
  return true;
};

/**
 * Hook for global shortcuts that work regardless of focus
 */
export const useGlobalShortcuts = (shortcuts: KeyboardShortcut[]) => {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Only handle global shortcuts for certain keys or when not in input fields
      const isGlobalKey = event.ctrlKey || event.metaKey || event.altKey;
      const shouldHandle = isGlobalKey || shouldHandleKeyboardEvent(event);
      
      if (!shouldHandle) {
        return;
      }

      for (const shortcut of shortcuts) {
        if (shortcut.enabled === false) {
          continue;
        }

        if (event.key.toLowerCase() !== shortcut.key.toLowerCase()) {
          continue;
        }

        const ctrlMatch = (shortcut.ctrlKey ?? false) === event.ctrlKey;
        const shiftMatch = (shortcut.shiftKey ?? false) === event.shiftKey;
        const altMatch = (shortcut.altKey ?? false) === event.altKey;
        const metaMatch = (shortcut.metaKey ?? false) === event.metaKey;

        if (ctrlMatch && shiftMatch && altMatch && metaMatch) {
          if (shortcut.preventDefault !== false) {
            event.preventDefault();
          }
          if (shortcut.stopPropagation !== false) {
            event.stopPropagation();
          }
          
          shortcut.action();
          break;
        }
      }
    },
    [shortcuts]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown, true);
    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [handleKeyDown]);
};
