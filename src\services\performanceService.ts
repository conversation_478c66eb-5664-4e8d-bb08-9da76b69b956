/**
 * Performance optimization service for the Prompt Enhancer app
 * Handles debouncing, caching, and memory management
 */

export class PerformanceService {
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private readonly DEFAULT_DEBOUNCE_DELAY = 300;
  private readonly DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 100;

  /**
   * Debounce function calls to prevent excessive API requests
   */
  debounce<T extends (...args: any[]) => any>(
    key: string,
    func: T,
    delay: number = this.DEFAULT_DEBOUNCE_DELAY
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      // Clear existing timer
      const existingTimer = this.debounceTimers.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      // Set new timer
      const timer = setTimeout(() => {
        func(...args);
        this.debounceTimers.delete(key);
      }, delay);

      this.debounceTimers.set(key, timer);
    };
  }

  /**
   * Cache data with TTL (Time To Live)
   */
  setCache(key: string, data: any, ttl: number = this.DEFAULT_CACHE_TTL): void {
    // Clean up old entries if cache is getting too large
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.cleanupCache();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Get cached data if it exists and hasn't expired
   */
  getCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }

    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  /**
   * Clear specific cache entry
   */
  clearCache(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clearAllCache(): void {
    this.cache.clear();
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, cached] of this.cache.entries()) {
      if (now - cached.timestamp > cached.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));

    // If still too large, remove oldest entries
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, Math.floor(this.MAX_CACHE_SIZE / 2));
      toRemove.forEach(([key]) => this.cache.delete(key));
    }
  }

  /**
   * Generate cache key for enhancement requests
   */
  generateEnhancementCacheKey(
    prompt: string,
    mode: string,
    style: string
  ): string {
    const hash = this.simpleHash(prompt + mode + style);
    return `enhancement_${hash}`;
  }

  /**
   * Simple hash function for cache keys
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Throttle function calls to limit frequency
   */
  throttle<T extends (...args: any[]) => any>(
    key: string,
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle = false;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => {
          inThrottle = false;
        }, limit);
      }
    };
  }

  /**
   * Batch multiple operations together
   */
  batch<T>(
    operations: (() => Promise<T>)[],
    batchSize: number = 5,
    delay: number = 100
  ): Promise<T[]> {
    return new Promise((resolve, reject) => {
      const results: T[] = [];
      let currentIndex = 0;

      const processBatch = async () => {
        const batch = operations.slice(currentIndex, currentIndex + batchSize);
        
        try {
          const batchResults = await Promise.all(batch.map(op => op()));
          results.push(...batchResults);
          currentIndex += batchSize;

          if (currentIndex < operations.length) {
            setTimeout(processBatch, delay);
          } else {
            resolve(results);
          }
        } catch (error) {
          reject(error);
        }
      };

      processBatch();
    });
  }

  /**
   * Memory usage monitoring
   */
  getMemoryUsage(): {
    cacheSize: number;
    debounceTimers: number;
    estimatedMemoryMB: number;
  } {
    const cacheSize = this.cache.size;
    const debounceTimers = this.debounceTimers.size;
    
    // Rough estimation of memory usage
    let estimatedBytes = 0;
    for (const [key, cached] of this.cache.entries()) {
      estimatedBytes += key.length * 2; // UTF-16 characters
      estimatedBytes += JSON.stringify(cached.data).length * 2;
      estimatedBytes += 24; // Object overhead
    }

    return {
      cacheSize,
      debounceTimers,
      estimatedMemoryMB: Math.round(estimatedBytes / (1024 * 1024) * 100) / 100,
    };
  }

  /**
   * Cleanup all resources
   */
  cleanup(): void {
    // Clear all debounce timers
    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer);
    }
    this.debounceTimers.clear();

    // Clear cache
    this.cache.clear();
  }
}

// Export singleton instance
export const performanceService = new PerformanceService();
