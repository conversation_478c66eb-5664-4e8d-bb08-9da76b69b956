@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
}

#root {
  width: 100%;
  height: 100vh;
  min-height: 600px;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Primary color variables */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
}

/* Custom component styles */
.diff-viewer {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
}

/* Animation for loading states */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced minimized sidebar styles */
.minimized-sidebar-button {
  transition: all 0.2s ease-in-out;
  position: relative;
}

.minimized-sidebar-button:hover {
  transform: translateX(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.minimized-sidebar-button:active {
  transform: translateX(-1px);
}

/* Notification badges */
.notification-badge {
  animation: badge-appear 0.3s ease-out;
}

@keyframes badge-appear {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Progress bar animation */
.progress-bar-fill {
  transition: height 0.5s ease-in-out;
}

/* Hover tooltips for minimized sidebar */
.minimized-tooltip {
  position: absolute;
  left: -8px;
  transform: translateX(-100%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
  z-index: 1000;
}

.minimized-sidebar-button:hover .minimized-tooltip {
  opacity: 1;
}

/* Clickable header styles */
.tools-header-button {
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease-in-out;
}

.tools-header-button:hover {
  background-color: rgba(249, 250, 251, 1);
  transform: translateY(-1px);
}

.tools-header-button:active {
  background-color: rgba(243, 244, 246, 1);
  transform: scale(0.995) translateY(0px);
}

/* Enhanced tab animations */
.tab-button {
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  transition: left 0.5s ease-in-out;
}

.tab-button:hover::before {
  left: 100%;
}
