import { describe, it, expect, vi, beforeEach, after<PERSON><PERSON>, <PERSON><PERSON> } from "vitest";
import { BatchService } from "../batchService";
import { BatchItem } from "../../types";
import { geminiService } from "../geminiService";

// Mock the geminiService
vi.mock("../geminiService", () => ({
  geminiService: {
    enhancePrompt: vi.fn(),
  },
}));

describe("BatchService", () => {
  let service: BatchService;

  beforeEach(() => {
    service = new BatchService();
    vi.clearAllMocks();
  });

  describe("parseTextFile", () => {
    it("should parse a text file into an array of strings, trimming whitespace", async () => {
      const fileContent = "  prompt 1  \r\nprompt 2\n\nprompt 3";
      const file = new File([fileContent], "prompts.txt", {
        type: "text/plain",
      });
      const result = await service.parseTextFile(file);
      expect(result).toEqual(["prompt 1", "prompt 2", "prompt 3"]);
    });

    it("should resolve with an empty array for an empty file", async () => {
      const file = new File([], "empty.txt", { type: "text/plain" });
      const result = await service.parseTextFile(file);
      expect(result).toEqual([]);
    });

    it("should reject with an error if file reading fails", async () => {
      const file = new File(["content"], "test.txt", { type: "text/plain" });
      const mockReader = {
        readAsText: vi.fn(),
        // Manually set onload/onerror
        onload: null as any,
        onerror: null as any,
      };
      vi.spyOn(window, "FileReader").mockImplementation(
        () => mockReader as any
      );

      const promise = service.parseTextFile(file);
      // Simulate an error event
      mockReader.onerror(new ProgressEvent("error"));

      await expect(promise).rejects.toThrow("Failed to read file");
    });
  });

  describe("validateBatchSize", () => {
    it("should return valid for a correct batch size", () => {
      const items = [{ originalPrompt: "p1" }, { originalPrompt: "p2" }];
      const result = service.validateBatchSize(items as BatchItem[]);
      expect(result.isValid).toBe(true);
    });

    it("should return invalid for an empty batch", () => {
      const result = service.validateBatchSize([]);
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("No items to process");
    });

    it("should return invalid when batch size exceeds the maximum", () => {
      const items = Array(51).fill({ originalPrompt: "p" });
      const result = service.validateBatchSize(items as BatchItem[]);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain(
        "Batch size too large. Maximum 50 items allowed."
      );
    });

    it("should return invalid if some items have empty prompts", () => {
      const items = [{ originalPrompt: "p1" }, { originalPrompt: "  " }];
      const result = service.validateBatchSize(items as BatchItem[]);
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("1 items have empty prompts");
    });
  });

  describe("exportResults", () => {
    const items: BatchItem[] = [
      {
        id: "1",
        originalPrompt: "P1",
        enhancedPrompt: "E1",
        status: "completed",
      },
      {
        id: "2",
        originalPrompt: "P2",
        status: "error",
        error: "API Error",
      },
      {
        id: "3",
        originalPrompt: 'P3 with "quotes"',
        enhancedPrompt: 'E3 with "quotes"',
        status: "completed",
      },
    ];

    it("should export completed items as JSON", async () => {
      const result = await service.exportResults(items, "json");
      const parsed = JSON.parse(result);
      expect(parsed).toHaveLength(2);
      expect(parsed[0].originalPrompt).toBe("P1");
      expect(parsed[1].originalPrompt).toBe('P3 with "quotes"');
    });

    it("should export all items as CSV, handling quotes", async () => {
      const result = await service.exportResults(items, "csv");
      const rows = result.split("\n");
      expect(rows).toHaveLength(4); // Header + 3 items
      expect(rows[0]).toBe("Original Prompt,Enhanced Prompt,Status,Error");
      expect(rows[1]).toBe('"P1","E1",completed,""');
      expect(rows[2]).toBe('"P2","",error,"API Error"');
      expect(rows[3]).toBe(
        '"P3 with ""quotes""","E3 with ""quotes""",completed,""'
      );
    });

    it("should export all items as TXT", async () => {
      const result = await service.exportResults(items, "txt");
      expect(result).toContain("=== Item 1 ===");
      expect(result).toContain("Status: completed");
      expect(result).toContain("Original: P1");
      expect(result).toContain("Enhanced: E1");
      expect(result).toContain("=== Item 2 ===");
      expect(result).toContain("Status: error");
      expect(result).toContain("Error: API Error");
    });

    it("should throw an error for unsupported format", async () => {
      await expect(service.exportResults(items, "xml" as any)).rejects.toThrow(
        "Unsupported export format: xml"
      );
    });
  });

  describe("processBatch", () => {
    beforeEach(() => {
      vi.useFakeTimers();
      // Mock the delay function to avoid waiting in tests
      vi.spyOn(BatchService.prototype as any, "delay").mockResolvedValue(
        undefined
      );
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it("should process a batch successfully, calling progress and complete callbacks", async () => {
      const items: BatchItem[] = [
        { id: "1", originalPrompt: "P1", status: "pending" },
        { id: "2", originalPrompt: "P2", status: "pending" },
      ];
      const onProgress = vi.fn();
      const onComplete = vi.fn();

      (geminiService.enhancePrompt as Mock)
        .mockResolvedValueOnce({ enhancedPrompt: "E1" })
        .mockResolvedValueOnce({ enhancedPrompt: "E2" });

      const promise = service.processBatch(
        items,
        "concise",
        onProgress,
        onComplete
      );
      await vi.runAllTimersAsync();
      await promise;

      // Check onProgress calls
      expect(onProgress).toHaveBeenCalled();
      const lastProgressCall =
        onProgress.mock.calls[onProgress.mock.calls.length - 2][0];
      expect(lastProgressCall.completedItems).toBe(2);
      expect(lastProgressCall.failedItems).toBe(0);

      // Check onComplete call
      expect(onComplete).toHaveBeenCalledTimes(1);
      const finalResults = onComplete.mock.calls[0][0];
      expect(finalResults[0].status).toBe("completed");
      expect(finalResults[0].enhancedPrompt).toBe("E1");
      expect(finalResults[1].status).toBe("completed");
      expect(finalResults[1].enhancedPrompt).toBe("E2");
    });

    it("should handle processing errors and update item status", async () => {
      const items: BatchItem[] = [
        { id: "1", originalPrompt: "P1", status: "pending" },
        { id: "2", originalPrompt: "P2", status: "pending" },
      ];
      const onProgress = vi.fn();
      const onComplete = vi.fn();

      (geminiService.enhancePrompt as Mock)
        .mockResolvedValueOnce({ enhancedPrompt: "E1" })
        .mockRejectedValueOnce(new Error("API Failure"));

      const promise = service.processBatch(
        items,
        "creative",
        onProgress,
        onComplete
      );
      await vi.runAllTimersAsync();
      await promise;

      const finalResults = onComplete.mock.calls[0][0];
      expect(finalResults[0].status).toBe("completed");
      expect(finalResults[1].status).toBe("error");
      expect(finalResults[1].error).toBe("API Failure");

      const lastProgressCall =
        onProgress.mock.calls[onProgress.mock.calls.length - 2][0];
      expect(lastProgressCall.completedItems).toBe(1);
      expect(lastProgressCall.failedItems).toBe(1);
    });

    it("should stop processing when stopProcessing is called", async () => {
      const items: BatchItem[] = [
        { id: "1", originalPrompt: "P1", status: "pending" },
        { id: "2", originalPrompt: "P2", status: "pending" },
      ];
      const onProgress = vi.fn();
      const onComplete = vi.fn();

      (geminiService.enhancePrompt as Mock).mockImplementation(async () => {
        // Stop processing after the first item is done
        service.stopProcessing();
        return { enhancedPrompt: "E1" };
      });

      const promise = service.processBatch(
        items,
        "concise",
        onProgress,
        onComplete
      );
      await vi.runAllTimersAsync();
      await promise;

      expect(geminiService.enhancePrompt).toHaveBeenCalledTimes(1);
      const finalResults = onComplete.mock.calls[0][0];
      expect(finalResults[0].status).toBe("completed");
      expect(finalResults[1].status).toBe("pending");
    });
  });
});
