/**
 * Notification service for user feedback and system notifications
 */

export interface NotificationOptions {
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
  silent?: boolean;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: "primary" | "secondary" | "danger";
}

export interface ToastNotification extends NotificationOptions {
  id: string;
  timestamp: Date;
  dismissed: boolean;
}

export class NotificationService {
  private toasts: ToastNotification[] = [];
  private listeners: ((toasts: ToastNotification[]) => void)[] = [];
  private readonly MAX_TOASTS = 5;
  private readonly DEFAULT_DURATION = 5000;

  /**
   * Show a toast notification
   */
  showToast(options: NotificationOptions): string {
    const id = this.generateId();
    const toast: ToastNotification = {
      ...options,
      id,
      timestamp: new Date(),
      dismissed: false,
      duration: options.duration ?? this.DEFAULT_DURATION,
    };

    this.toasts.unshift(toast);

    // Remove excess toasts
    if (this.toasts.length > this.MAX_TOASTS) {
      this.toasts = this.toasts.slice(0, this.MAX_TOASTS);
    }

    // Auto-dismiss if not persistent
    if (!options.persistent && toast?.duration) {
      setTimeout(() => {
        this.dismissToast(id);
      }, toast.duration);
    }

    this.notifyListeners();
    return id;
  }

  /**
   * Show system notification (Electron)
   */
  async showSystemNotification(options: NotificationOptions): Promise<boolean> {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.showNotification({
          title: options.title,
          body: options.message,
          silent: options.silent,
        });
        return result.success;
      } catch (error) {
        console.error("Failed to show system notification:", error);
        return false;
      }
    }
    return false;
  }

  /**
   * Show success notification
   */
  success(message: string, title: string = "Success"): string {
    return this.showToast({
      title,
      message,
      type: "success",
    });
  }

  /**
   * Show error notification
   */
  error(
    message: string,
    title: string = "Error",
    persistent: boolean = false
  ): string {
    return this.showToast({
      title,
      message,
      type: "error",
      persistent,
      duration: persistent ? 0 : 8000, // Longer duration for errors
    });
  }

  /**
   * Show warning notification
   */
  warning(message: string, title: string = "Warning"): string {
    return this.showToast({
      title,
      message,
      type: "warning",
      duration: 6000,
    });
  }

  /**
   * Show info notification
   */
  info(message: string, title: string = "Info"): string {
    return this.showToast({
      title,
      message,
      type: "info",
    });
  }

  /**
   * Show notification with actions
   */
  showWithActions(
    message: string,
    actions: NotificationAction[],
    title: string = "Action Required",
    type: "info" | "warning" = "info"
  ): string {
    return this.showToast({
      title,
      message,
      type,
      actions,
      persistent: true, // Action notifications should be persistent
    });
  }

  /**
   * Dismiss a specific toast
   */
  dismissToast(id: string): void {
    const index = this.toasts.findIndex((toast) => toast.id === id);
    if (index !== -1) {
      this.toasts[index].dismissed = true;
      setTimeout(() => {
        this.toasts = this.toasts.filter((toast) => toast.id !== id);
        this.notifyListeners();
      }, 300); // Allow for exit animation
    }
    this.notifyListeners();
  }

  /**
   * Dismiss all toasts
   */
  dismissAll(): void {
    this.toasts.forEach((toast) => {
      toast.dismissed = true;
    });
    setTimeout(() => {
      this.toasts = [];
      this.notifyListeners();
    }, 300);
    this.notifyListeners();
  }

  /**
   * Get current toasts
   */
  getToasts(): ToastNotification[] {
    return [...this.toasts];
  }

  /**
   * Subscribe to toast updates
   */
  subscribe(listener: (toasts: ToastNotification[]) => void): () => void {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index !== -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Show progress notification
   */
  showProgress(title: string, message: string, progress: number = 0): string {
    const id = this.generateId();
    const toast: ToastNotification = {
      title,
      message: `${message} (${Math.round(progress)}%)`,
      type: "info",
      id,
      timestamp: new Date(),
      dismissed: false,
      persistent: true,
    };

    // Update existing progress toast or create new one
    const existingIndex = this.toasts.findIndex(
      (t) => t.title === title && t.type === "info" && t.persistent
    );

    if (existingIndex !== -1) {
      this.toasts[existingIndex] = toast;
    } else {
      this.toasts.unshift(toast);
    }

    this.notifyListeners();
    return id;
  }

  /**
   * Update progress notification
   */
  updateProgress(id: string, message?: string): void {
    const toast = this.toasts.find((t) => t.id === id);
    if (toast) {
      if (message) {
        toast.message = message;
      } else {
        toast.message = toast.message.replace(/\s*\(\d+%\)$/, "");
      }
      this.notifyListeners();
    }
  }

  /**
   * Complete progress notification
   */
  completeProgress(id: string, successMessage?: string): void {
    const toast = this.toasts.find((t) => t.id === id);
    if (toast) {
      toast.type = "success";
      toast.message = successMessage || "Completed successfully";
      toast.persistent = false;

      // Auto-dismiss after 3 seconds
      setTimeout(() => {
        this.dismissToast(id);
      }, 3000);

      this.notifyListeners();
    }
  }

  /**
   * Fail progress notification
   */
  failProgress(id: string, errorMessage?: string): void {
    const toast = this.toasts.find((t) => t.id === id);
    if (toast) {
      toast.type = "error";
      toast.message = errorMessage || "Operation failed";
      toast.persistent = false;

      // Auto-dismiss after 8 seconds
      setTimeout(() => {
        this.dismissToast(id);
      }, 8000);

      this.notifyListeners();
    }
  }

  /**
   * Show clipboard success notification
   */
  clipboardSuccess(content: string = ""): string {
    const preview =
      content.length > 50 ? content.substring(0, 50) + "..." : content;
    return this.success(
      preview ? `Copied: "${preview}"` : "Copied to clipboard",
      "Clipboard"
    );
  }

  /**
   * Show API key related notifications
   */
  apiKeySuccess(): string {
    return this.success("API key saved successfully", "Settings");
  }

  apiKeyError(): string {
    return this.error("Failed to save API key. Please try again.", "Settings");
  }

  apiKeyInvalid(): string {
    return this.error(
      "Invalid API key. Please check your key and try again.",
      "Authentication"
    );
  }

  /**
   * Show enhancement related notifications
   */
  enhancementSuccess(): string {
    return this.success("Prompt enhanced successfully");
  }

  enhancementError(error: string): string {
    return this.error(`Enhancement failed: ${error}`, "Enhancement Error");
  }

  enhancementStarted(): string {
    return this.info("Enhancing prompt...", "Processing");
  }

  /**
   * Generate unique ID for notifications
   */
  private generateId(): string {
    return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Notify all listeners of toast updates
   */
  private notifyListeners(): void {
    this.listeners.forEach((listener) => {
      try {
        listener([...this.toasts]);
      } catch (error) {
        console.error("Error in notification listener:", error);
      }
    });
  }

  /**
   * Clear all notifications and listeners
   */
  cleanup(): void {
    this.toasts = [];
    this.listeners = [];
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
