import { PromptHistory, EnhancementMode, EnhancementStyle } from "../types";

export class HistoryService {
  private readonly STORAGE_KEY = "prompt-enhancer-history";
  private readonly MAX_HISTORY_ITEMS = 100;

  async saveToHistory(
    originalPrompt: string,
    enhancedPrompt: string,
    mode: EnhancementMode,
    style: EnhancementStyle,
    options?: {
      category?: string;
      tags?: string[];
      rating?: number;
      notes?: string;
    }
  ): Promise<PromptHistory> {
    const historyItem: PromptHistory = {
      id: this.generateId(),
      timestamp: new Date(),
      originalPrompt,
      enhancedPrompt,
      mode,
      style,
      category: options?.category,
      tags: options?.tags,
      rating: options?.rating,
      notes: options?.notes,
    };

    const history = await this.getHistory();
    history.unshift(historyItem);

    // Keep only the most recent items
    if (history.length > this.MAX_HISTORY_ITEMS) {
      history.splice(this.MAX_HISTORY_ITEMS);
    }

    await this.saveHistory(history);
    return historyItem;
  }

  async getHistory(): Promise<PromptHistory[]> {
    try {
      if (window.electronAPI) {
        // In Electron, use the main process for file operations
        const data = localStorage.getItem(this.STORAGE_KEY);
        if (data) {
          const parsed = JSON.parse(data);
          return parsed.map((item: any) => ({
            ...item,
            timestamp: new Date(item.timestamp),
          }));
        }
      } else {
        // Fallback to localStorage for web version
        const data = localStorage.getItem(this.STORAGE_KEY);
        if (data) {
          const parsed = JSON.parse(data);
          return parsed.map((item: any) => ({
            ...item,
            timestamp: new Date(item.timestamp),
          }));
        }
      }
      return [];
    } catch (error) {
      console.error("Failed to load history:", error);
      return [];
    }
  }

  async deleteHistoryItem(id: string): Promise<void> {
    const history = await this.getHistory();
    const filteredHistory = history.filter((item) => item.id !== id);
    await this.saveHistory(filteredHistory);
  }

  async clearHistory(): Promise<void> {
    await this.saveHistory([]);
  }

  async updateHistoryItem(
    id: string,
    updates: Partial<Omit<PromptHistory, "id" | "timestamp">>
  ): Promise<void> {
    const history = await this.getHistory();
    const index = history.findIndex((item) => item.id === id);

    if (index !== -1) {
      history[index] = { ...history[index], ...updates };
      await this.saveHistory(history);
    }
  }

  async exportHistory(format: "json" | "csv" = "json"): Promise<string> {
    const history = await this.getHistory();

    if (format === "csv") {
      const headers = [
        "ID",
        "Timestamp",
        "Mode",
        "Style",
        "Category",
        "Tags",
        "Rating",
        "Original Prompt",
        "Enhanced Prompt",
        "Notes",
      ];

      const rows = history.map((item) => [
        item.id,
        item.timestamp.toISOString(),
        item.mode,
        item.style,
        item.category || "",
        item.tags?.join(";") || "",
        item.rating?.toString() || "",
        `"${item.originalPrompt.replace(/"/g, '""')}"`,
        `"${item.enhancedPrompt.replace(/"/g, '""')}"`,
        `"${(item.notes || "").replace(/"/g, '""')}"`,
      ]);

      return [headers.join(","), ...rows.map((row) => row.join(","))].join(
        "\n"
      );
    } else {
      return JSON.stringify(history, null, 2);
    }
  }

  async importHistory(
    data: string,
    format: "json" | "csv" = "json"
  ): Promise<number> {
    try {
      let importedItems: PromptHistory[] = [];

      if (format === "json") {
        const parsed = JSON.parse(data);
        importedItems = Array.isArray(parsed) ? parsed : [parsed];
      } else {
        // CSV parsing would be more complex, implement if needed
        throw new Error("CSV import not yet implemented");
      }

      // Validate and sanitize imported items
      const validItems = importedItems
        .filter(this.isValidHistoryItem)
        .map((item) => ({
          ...item,
          id: this.generateId(), // Generate new IDs to avoid conflicts
          timestamp: new Date(item.timestamp),
        }));

      if (validItems.length > 0) {
        const existingHistory = await this.getHistory();
        const mergedHistory = [...validItems, ...existingHistory];

        // Keep only the most recent items
        if (mergedHistory.length > this.MAX_HISTORY_ITEMS) {
          mergedHistory.splice(this.MAX_HISTORY_ITEMS);
        }

        await this.saveHistory(mergedHistory);
      }

      return validItems.length;
    } catch (error: any) {
      console.error("Failed to import history:", error);
      if (error.message === "CSV import not yet implemented") {
        throw error; // Re-throw the specific error for testing purposes
      }
      throw new Error("Invalid history data format");
    }
  }

  private async saveHistory(history: PromptHistory[]): Promise<void> {
    try {
      const data = JSON.stringify(history);
      localStorage.setItem(this.STORAGE_KEY, data);
    } catch (error) {
      console.error("Failed to save history:", error);
      throw new Error("Failed to save history");
    }
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private isValidHistoryItem(item: any): item is PromptHistory {
    return (
      typeof item === "object" &&
      typeof item.id === "string" &&
      typeof item.originalPrompt === "string" &&
      typeof item.enhancedPrompt === "string" &&
      typeof item.mode === "string" &&
      typeof item.style === "string" &&
      (item.timestamp instanceof Date || typeof item.timestamp === "string")
    );
  }

  async getStatistics(): Promise<{
    totalItems: number;
    modeDistribution: Record<EnhancementMode, number>;
    styleDistribution: Record<EnhancementStyle, number>;
    averageRating: number;
    recentActivity: { date: string; count: number }[];
  }> {
    const history = await this.getHistory();

    const modeDistribution = history.reduce((acc, item) => {
      acc[item.mode] = (acc[item.mode] || 0) + 1;
      return acc;
    }, {} as Record<EnhancementMode, number>);

    const styleDistribution = history.reduce((acc, item) => {
      acc[item.style] = (acc[item.style] || 0) + 1;
      return acc;
    }, {} as Record<EnhancementStyle, number>);

    const ratingsSum = history
      .filter((item) => item.rating)
      .reduce((sum, item) => sum + (item.rating || 0), 0);
    const ratingsCount = history.filter((item) => item.rating).length;
    const averageRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

    // Recent activity (last 7 days)
    const now = new Date();
    const recentActivity = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split("T")[0];

      const count = history.filter((item) => {
        const itemDate = new Date(item.timestamp).toISOString().split("T")[0];
        return itemDate === dateStr;
      }).length;

      recentActivity.push({ date: dateStr, count });
    }

    return {
      totalItems: history.length,
      modeDistribution,
      styleDistribution,
      averageRating,
      recentActivity,
    };
  }
}

export const historyService = new HistoryService();
